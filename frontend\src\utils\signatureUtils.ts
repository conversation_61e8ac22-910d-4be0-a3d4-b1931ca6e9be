/**
 * Utility functions for handling signatures in documents
 */

/**
 * Converts a base64 signature to a buffer for use in docxtemplater
 * @param base64Signature Base64 encoded signature image
 * @returns Buffer containing the image data
 */
export function base64ToBuffer(base64Signature: string): ArrayBuffer {
  // Remove the data URL prefix if present
  const base64Data = base64Signature.replace(/^data:image\/[a-z]+;base64,/, '');
  
  // Convert base64 to binary string
  const binaryString = atob(base64Data);
  
  // Create array buffer
  const buffer = new ArrayBuffer(binaryString.length);
  const view = new Uint8Array(buffer);
  
  for (let i = 0; i < binaryString.length; i++) {
    view[i] = binaryString.charCodeAt(i);
  }
  
  return buffer;
}

/**
 * Checks if a value is a base64 signature
 * @param value Value to check
 * @returns True if the value is a base64 signature
 */
export function isBase64Signature(value: any): boolean {
  return typeof value === 'string' && value.startsWith('data:image/');
}

/**
 * Processes form data to convert signatures to the format needed for docxtemplater
 * @param data Form data containing signatures
 * @returns Processed data with signatures converted to image objects
 */
export function processSignaturesForTemplate(data: Record<string, any>): Record<string, any> {
  const processedData = { ...data };

  // Known signature field names
  const signatureFields = ['klant_handtekening', 'monteur_handtekening'];

  signatureFields.forEach(key => {
    const value = processedData[key];

    console.log(`Processing signature field ${key}:`, value ? 'has value' : 'empty');

    // Check if this is a signature field with base64 data
    if (isBase64Signature(value) && !isSignatureEmpty(value)) {
      try {
        const buffer = base64ToBuffer(value);

        // Create image object for docxtemplater-image-module
        processedData[key] = {
          _type: 'image',
          buffer: buffer,
          width: 150, // Width in pixels
          height: 75,  // Height in pixels
          extension: '.png'
        };
        console.log(`Converted ${key} to image object`);
      } catch (error) {
        console.error(`Error processing signature ${key}:`, error);
        // Fallback to placeholder text
        processedData[key] = 'Handtekening niet beschikbaar';
      }
    } else {
      // For empty or invalid signatures, use placeholder text
      processedData[key] = 'Niet ondertekend';
      console.log(`Using placeholder for ${key}`);
    }
  });

  return processedData;
}

/**
 * Gets the file extension from a base64 data URL
 * @param base64Data Base64 data URL
 * @returns File extension (e.g., '.png', '.jpg')
 */
export function getImageExtensionFromBase64(base64Data: string): string {
  const match = base64Data.match(/^data:image\/([a-z]+);base64,/);
  if (match) {
    const format = match[1];
    return format === 'jpeg' ? '.jpg' : `.${format}`;
  }
  return '.png'; // Default fallback
}

/**
 * Validates that a signature is not empty
 * @param signature Base64 signature string
 * @returns True if signature contains actual drawing data
 */
export function isSignatureEmpty(signature: string): boolean {
  if (!signature || !isBase64Signature(signature)) {
    return true;
  }
  
  // A very basic check - empty signatures are usually very small
  // This is a simple heuristic and could be improved
  const base64Data = signature.replace(/^data:image\/[a-z]+;base64,/, '');
  return base64Data.length < 100; // Arbitrary threshold
}

/**
 * Creates a placeholder image for empty signatures
 * @returns Base64 encoded placeholder image
 */
export function createSignaturePlaceholder(): string {
  // Create a simple placeholder using canvas
  const canvas = document.createElement('canvas');
  canvas.width = 150;
  canvas.height = 75;
  const ctx = canvas.getContext('2d');
  
  if (ctx) {
    // Fill with light gray background
    ctx.fillStyle = '#f5f5f5';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Add border
    ctx.strokeStyle = '#d1d5db';
    ctx.lineWidth = 1;
    ctx.strokeRect(0, 0, canvas.width, canvas.height);
    
    // Add text
    ctx.fillStyle = '#9ca3af';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Niet ondertekend', canvas.width / 2, canvas.height / 2);
  }
  
  return canvas.toDataURL('image/png');
}
