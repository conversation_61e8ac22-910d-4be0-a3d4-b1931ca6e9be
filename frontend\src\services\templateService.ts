import <PERSON>z<PERSON><PERSON> from 'pizzip';
import Docxtemplater from 'docxtemplater';
import { processSignaturesForTemplate, isBase64Signature } from '../utils/signatureUtils';
import { saveAs } from 'file-saver';
import { DocumentTemplate } from '../types/document_template';
import { getTemplateFileUrl } from './documentTemplateService';

/**
 * Service for handling document templates using Docxtemplater
 */
export class TemplateService {
  /**
   * Loads a template from a URL and returns the template as an ArrayBuffer
   * @param templateId ID of the template to load
   * @returns Promise resolving to the template ArrayBuffer
   */
  static async loadTemplate(templateId: number): Promise<ArrayBuffer> {
    try {
      const response = await fetch(getTemplateFileUrl(templateId));
      if (!response.ok) {
        throw new Error(`Failed to load template: ${response.statusText}`);
      }
      return await response.arrayBuffer();
    } catch (error) {
      console.error('Error loading template:', error);
      throw error;
    }
  }

  /**
   * Fills a template with data and returns the filled document as a blob
   * @param templateContent Template content as ArrayBuffer
   * @param data Data to fill the template with
   * @returns Promise resolving to the filled document blob
   */
  static async fillTemplate(templateContent: ArrayBuffer, data: Record<string, any>): Promise<Blob> {
    try {
      console.log('Original data:', data);

      // Process data to convert signatures to a simpler format
      const processedData = this.processDataForTemplate(data);

      console.log('Processed data:', processedData);

      // Load the template
      const zip = new PizZip(templateContent);

      // Create a new instance of Docxtemplater
      const doc = new Docxtemplater(zip, {
        paragraphLoop: true,
        linebreaks: true
      });

      // Render the document (replace all variables with their values)
      doc.render(processedData);

      // Get the zip document containing the filled template
      const out = doc.getZip().generate({
        type: 'blob',
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      });

      return out;
    } catch (error) {
      console.error('Error filling template:', error);
      throw error;
    }
  }

  /**
   * Process data for template, converting signatures to a format that works
   */
  private static processDataForTemplate(data: Record<string, any>): Record<string, any> {
    const processedData = { ...data };

    // Process signature fields
    const signatureFields = ['klant_handtekening', 'monteur_handtekening'];

    signatureFields.forEach(fieldName => {
      const signatureValue = processedData[fieldName];

      if (isBase64Signature(signatureValue)) {
        console.log(`Processing signature for ${fieldName}`);

        // For now, let's try a simple approach - create an HTML img tag
        // This might work in some Word processors
        const imgTag = `<img src="${signatureValue}" width="150" height="75" alt="Signature" />`;
        processedData[fieldName] = imgTag;

        console.log(`Converted ${fieldName} to HTML img tag`);
      } else {
        console.log(`No valid signature found for ${fieldName}`);
        processedData[fieldName] = 'Niet ondertekend';
      }
    });

    return processedData;
  }

  /**
   * Insert signature images directly into the Word document XML
   */
  private static async insertSignatureImages(zip: PizZip, data: Record<string, any>): Promise<void> {
    try {
      // Get the main document XML
      const docXml = zip.file('word/document.xml');
      if (!docXml) {
        console.warn('Could not find document.xml in template');
        return;
      }

      let documentContent = docXml.asText();
      let imageCounter = 1;

      // Debug: Log part of the document content to see the structure
      console.log('Document XML sample:', documentContent.substring(0, 1000));

      // Process signature fields
      const signatureFields = [
        { field: 'klant_handtekening', data: data.klant_handtekening },
        { field: 'monteur_handtekening', data: data.monteur_handtekening }
      ];

      for (const { field, data: signatureData } of signatureFields) {
        if (signatureData && isBase64Signature(signatureData)) {
          console.log(`Inserting signature image for ${field}`);

          // Convert base64 to binary
          const base64Data = signatureData.split(',')[1];
          const binaryData = atob(base64Data);
          const bytes = new Uint8Array(binaryData.length);
          for (let i = 0; i < binaryData.length; i++) {
            bytes[i] = binaryData.charCodeAt(i);
          }

          // Add image to media folder
          const imageFileName = `signature_${field}.png`;
          zip.file(`word/media/${imageFileName}`, bytes);

          // Create relationship ID
          const relationshipId = `rId${imageCounter + 100}`;

          // Create image XML for Word document
          const imageXml = this.createImageXml(relationshipId, 150, 75);

          // Replace the placeholder with the image XML
          const placeholder = `{${field}}`;
          documentContent = documentContent.replace(placeholder, imageXml);

          // Update relationships
          await this.addImageRelationship(zip, relationshipId, imageFileName);

          imageCounter++;
        }
      }

      // Update the document XML
      zip.file('word/document.xml', documentContent);

    } catch (error) {
      console.error('Error inserting signature images:', error);
      // Don't throw here, let the document generation continue
    }
  }

  /**
   * Create XML for an image in Word document format
   */
  private static createImageXml(relationshipId: string, width: number, height: number): string {
    const widthEmu = width * 9525; // Convert pixels to EMU (English Metric Units)
    const heightEmu = height * 9525;

    return `<w:r><w:drawing><wp:inline distT="0" distB="0" distL="0" distR="0"><wp:extent cx="${widthEmu}" cy="${heightEmu}"/><wp:effectExtent l="0" t="0" r="0" b="0"/><wp:docPr id="1" name="Signature"/><wp:cNvGraphicFramePr><a:graphicFrameLocks xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" noChangeAspect="1"/></wp:cNvGraphicFramePr><a:graphic xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main"><a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/picture"><pic:pic xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture"><pic:nvPicPr><pic:cNvPr id="1" name="Signature"/><pic:cNvPicPr/></pic:nvPicPr><pic:blipFill><a:blip r:embed="${relationshipId}"/><a:stretch><a:fillRect/></a:stretch></pic:blipFill><pic:spPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="${widthEmu}" cy="${heightEmu}"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom></pic:spPr></pic:pic></a:graphicData></a:graphic></wp:inline></w:drawing></w:r>`;
  }

  /**
   * Add image relationship to document relationships
   */
  private static async addImageRelationship(zip: PizZip, relationshipId: string, imageFileName: string): Promise<void> {
    try {
      const relsFile = zip.file('word/_rels/document.xml.rels');
      if (!relsFile) {
        console.warn('Could not find document relationships file');
        return;
      }

      let relsContent = relsFile.asText();

      // Add relationship for the image
      const relationshipXml = `<Relationship Id="${relationshipId}" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image" Target="media/${imageFileName}"/>`;

      // Insert before the closing </Relationships> tag
      relsContent = relsContent.replace('</Relationships>', relationshipXml + '</Relationships>');

      zip.file('word/_rels/document.xml.rels', relsContent);
    } catch (error) {
      console.error('Error updating document relationships:', error);
    }
  }

  /**
   * Downloads a filled template
   * @param blob Blob containing the filled template
   * @param fileName Name of the file to download
   */
  static downloadDocument(blob: Blob, fileName: string): void {
    saveAs(blob, fileName);
  }

  /**
   * Saves a filled template to the server
   * @param templateId ID of the template used
   * @param customerId ID of the customer
   * @param blob Blob containing the filled template
   * @param fileName Name of the file to save
   * @returns Promise resolving to the saved document
   */
  static async saveDocument(templateId: number, customerId: number, blob: Blob, fileName: string): Promise<any> {
    const { saveFilledTemplate } = await import('./documentTemplateService');
    return saveFilledTemplate(templateId, customerId, blob, fileName);
  }

  /**
   * Analyzes a template to extract form fields
   * @param templateContent Template content as ArrayBuffer
   * @returns Promise resolving to an object with field information
   */
  static async analyzeTemplate(templateContent: ArrayBuffer): Promise<{
    fields: Array<{name: string, label: string}>;
    checkboxes: Array<{name: string, label: string}>;
    structure?: any;
    isInspectionTemplate?: boolean;
  }> {
    try {
      // Load the template
      const zip = new PizZip(templateContent);

      // Get the main document content
      const content = zip.files['word/document.xml']?.asText() || '';

      // Check if this is an inspection template based on content
      const isInspectionTemplate = content.includes('Type installatie') ||
                                   content.includes('Centrale / kiezer') ||
                                   content.includes('inbraakmeldsysteem') ||
                                   content.includes('centrale_accu');

      if (isInspectionTemplate) {
        return this.analyzeInspectionTemplate(content);
      }

      // Extract fields using regex for regular templates
      const fieldsMap = new Map<string, string>();
      const checkboxesMap = new Map<string, string>();

      // Look for simple fields in the format {field_name}
      const simpleFieldRegex = /{([^{}#/]+)}/g;
      let match;

      while ((match = simpleFieldRegex.exec(content)) !== null) {
        const fieldName = match[1].trim();
        // Skip if it's part of a conditional
        if (!content.includes(`{#if ${fieldName}}`) && !fieldName.includes('if') && !fieldName.includes('else')) {
          fieldsMap.set(fieldName, this.formatFieldLabel(fieldName));
        }
      }

      // Look for conditional fields (checkboxes) in the format {#field_name} or {#if field_name}
      const conditionalFieldRegex = /{#(?:if\s+)?([^{}]+)}/g;

      while ((match = conditionalFieldRegex.exec(content)) !== null) {
        const fieldName = match[1].trim();
        // Skip if it contains "else" or other template keywords
        if (!fieldName.includes('else') && !fieldName.includes('/') && !fieldName.includes('^')) {
          checkboxesMap.set(fieldName, this.formatFieldLabel(fieldName));
        }
      }

      // Convert maps to arrays
      const fields = Array.from(fieldsMap).map(([name, label]) => ({ name, label }));
      const checkboxes = Array.from(checkboxesMap).map(([name, label]) => ({ name, label }));

      return { fields, checkboxes };
    } catch (error) {
      console.error('Error analyzing template:', error);
      throw error;
    }
  }

  /**
   * Analyzes inspection template structure
   */
  static analyzeInspectionTemplate(content: string) {
    const structure = {
      customerFields: [
        { name: 'bonnummer', label: 'Bonnummer', type: 'text', autoPopulate: true },
        { name: 'klantnummer', label: 'Klantnummer', type: 'text', autoPopulate: true },
        { name: 'bedrijf', label: 'Bedrijf', type: 'text', autoPopulate: true },
        { name: 'adres', label: 'Adres', type: 'text', autoPopulate: true },
        { name: 'telefoon', label: 'Telefoonnummer', type: 'text', autoPopulate: true },
        { name: 'contactpersoon', label: 'Contactpersoon', type: 'text', autoPopulate: true },
        { name: 'email', label: 'Email', type: 'text', autoPopulate: true },
        { name: 'type', label: 'Type', type: 'text', autoPopulate: false }
      ],
      installationTypes: [
        { name: 'inbraakmeldsysteem', label: 'Inbraakmeldsysteem' },
        { name: 'brandmeldsysteem', label: 'Brandmeldsysteem' },
        { name: 'cctv', label: 'CCTV' }
      ],
      sections: [
        {
          title: 'Centrale / kiezer',
          components: [
            { name: 'centrale_accu', label: 'Accu' },
            { name: 'centrale_voeding', label: 'Voeding' },
            { name: 'centrale_lusspanning', label: 'Lusspanning' },
            { name: 'centrale_uitlezing', label: 'Uitlezing' },
            { name: 'centrale_algemeen', label: 'Algemene werking' }
          ]
        },
        {
          title: 'Detectie',
          components: [
            { name: 'detectie_bevestiging', label: 'Bevestiging' },
            { name: 'detectie_werking', label: 'Werking' },
            { name: 'detectie_projectie', label: 'Projectie' },
            { name: 'detectie_algemeen', label: 'Detectie algemeen' }
          ]
        },
        {
          title: 'Bekabeling',
          components: [
            { name: 'bekabeling_bevestiging', label: 'Bevestiging' },
            { name: 'bekabeling_afscherming', label: 'Afscherming' }
          ]
        },
        {
          title: 'Signalering',
          components: [
            { name: 'signalering_bevestiging', label: 'Bevestiging' },
            { name: 'signalering_werking_flits', label: 'Werking flits' },
            { name: 'signalering_werking_sirene', label: 'Werking sirene' },
            { name: 'signalering_algemeen', label: 'Signalering algemeen' }
          ]
        },
        {
          title: 'Doormelding',
          components: [
            { name: 'doormelding_schakel', label: 'Schakelmelding' },
            { name: 'doormelding_inbraak', label: 'Inbraak' },
            { name: 'doormelding_overval', label: 'Overval' },
            { name: 'doormelding_brand', label: 'Brand' },
            { name: 'doormelding_technisch', label: 'Technisch' },
            { name: 'doormelding_contact', label: 'Contact gewenst MK' }
          ]
        }
      ],
      generalState: [
        { name: 'installatie_in_orde', label: 'Installatie in orde' },
        { name: 'installatie_niet_in_orde', label: 'Installatie niet in orde' },
        { name: 'ja_opmerking', label: 'Ja, opmerking', type: 'textarea' },
        { name: 'nee_onbekend', label: 'Nee / onbekend', type: 'textarea' }
      ],
      signatures: [
        { name: 'klant_naam', label: 'Klant naam', type: 'text' },
        { name: 'datum', label: 'Datum', type: 'date' },
        { name: 'klant_handtekening', label: 'Klant handtekening', type: 'signature' },
        { name: 'monteur_naam', label: 'Monteur naam', type: 'text' },
        { name: 'begin_tijd', label: 'Begin tijd', type: 'time' },
        { name: 'eind_tijd', label: 'Eind tijd', type: 'time' },
        { name: 'monteur_handtekening', label: 'Monteur handtekening', type: 'signature' }
      ]
    };

    return {
      fields: [],
      checkboxes: [],
      structure,
      isInspectionTemplate: true
    };
  }

  /**
   * Formats a field name into a human-readable label
   * @param fieldName The raw field name from the template
   * @returns A formatted label
   */
  static formatFieldLabel(fieldName: string): string {
    // Remove prefixes like "check_"
    let label = fieldName.replace(/^check_/, '');

    // Replace underscores with spaces
    label = label.replace(/_/g, ' ');

    // Capitalize first letter of each word
    label = label.replace(/\b\w/g, c => c.toUpperCase());

    return label;
  }
}

export default TemplateService;
