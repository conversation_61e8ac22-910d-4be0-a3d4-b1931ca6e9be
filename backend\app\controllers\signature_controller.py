"""
Controller for handling signature-related API endpoints
"""
from flask import Blueprint, request, jsonify
from app.decorators.auth import jwt_required, roles_required
from app.decorators.rate_limit import rate_limit
from app.decorators.cors import cors_enabled
from app.services.signature_service import SignatureService
import logging

logger = logging.getLogger(__name__)

signature_bp = Blueprint('signature', __name__)


@signature_bp.route('/validate', methods=['POST'])
@jwt_required()
@rate_limit("100/minute")
@cors_enabled
def validate_signature():
    """
    Validate a signature image.
    
    Request body:
        signature_data (str): Base64 encoded signature image
        
    Returns:
        JSON: Validation result
    """
    try:
        data = request.get_json()
        if not data or 'signature_data' not in data:
            return jsonify({'error': 'signature_data is required'}), 400
        
        signature_data = data['signature_data']
        is_valid = SignatureService.validate_signature(signature_data)
        
        return jsonify({
            'valid': is_valid,
            'message': 'Signature is valid' if is_valid else 'Signature is invalid or empty'
        }), 200
        
    except Exception as e:
        logger.error(f"Failed to validate signature: {str(e)}")
        return jsonify({'error': str(e)}), 500


@signature_bp.route('/save', methods=['POST'])
@jwt_required()
@roles_required("administrator", "monteur", "verkoper")
@rate_limit("50/minute")
@cors_enabled
def save_signature():
    """
    Save a signature image to storage.
    
    Request body:
        signature_data (str): Base64 encoded signature image
        customer_id (int): ID of the customer
        document_type (str, optional): Type of document
        
    Returns:
        JSON: Storage information
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'Request body is required'}), 400
        
        signature_data = data.get('signature_data')
        customer_id = data.get('customer_id')
        document_type = data.get('document_type', 'signature')
        
        if not signature_data:
            return jsonify({'error': 'signature_data is required'}), 400
        
        if not customer_id:
            return jsonify({'error': 'customer_id is required'}), 400
        
        # Validate signature first
        if not SignatureService.validate_signature(signature_data):
            return jsonify({'error': 'Invalid or empty signature'}), 400
        
        # Save signature
        file_url, storage_path = SignatureService.save_signature(
            signature_data, customer_id, document_type
        )
        
        return jsonify({
            'message': 'Signature saved successfully',
            'file_url': file_url,
            'storage_path': storage_path
        }), 201
        
    except Exception as e:
        logger.error(f"Failed to save signature: {str(e)}")
        return jsonify({'error': str(e)}), 500


@signature_bp.route('/placeholder', methods=['GET'])
@jwt_required()
@rate_limit("100/minute")
@cors_enabled
def get_signature_placeholder():
    """
    Get a placeholder image for missing signatures.
    
    Returns:
        JSON: Placeholder image data
    """
    try:
        placeholder = SignatureService.create_signature_placeholder()
        
        return jsonify({
            'placeholder': placeholder,
            'message': 'Signature placeholder generated'
        }), 200
        
    except Exception as e:
        logger.error(f"Failed to generate signature placeholder: {str(e)}")
        return jsonify({'error': str(e)}), 500
