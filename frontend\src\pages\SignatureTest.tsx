import React, { useState } from 'react';
import SignaturePad from '../components/SignaturePad';
import SignatureField from '../components/SignatureField';
import SignatureService from '../services/signatureService';
import { processSignaturesForTemplate } from '../utils/signatureUtils';

const SignatureTest: React.FC = () => {
  const [customerSignature, setCustomerSignature] = useState('');
  const [mechanicSignature, setMechanicSignature] = useState('');
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const handleFieldChange = (name: string, value: string) => {
    if (name === 'customer_signature') {
      setCustomerSignature(value);
    } else if (name === 'mechanic_signature') {
      setMechanicSignature(value);
    }
  };

  const testSignatureValidation = async () => {
    setIsLoading(true);
    addTestResult('Testing signature validation...');

    try {
      if (customerSignature) {
        const result = await SignatureService.validateSignature(customerSignature);
        addTestResult(`Customer signature validation: ${result.valid ? 'VALID' : 'INVALID'} - ${result.message}`);
      } else {
        addTestResult('Customer signature is empty');
      }

      if (mechanicSignature) {
        const result = await SignatureService.validateSignature(mechanicSignature);
        addTestResult(`Mechanic signature validation: ${result.valid ? 'VALID' : 'INVALID'} - ${result.message}`);
      } else {
        addTestResult('Mechanic signature is empty');
      }
    } catch (error: any) {
      addTestResult(`Validation error: ${error.message}`);
    }

    setIsLoading(false);
  };

  const testSignatureSaving = async () => {
    setIsLoading(true);
    addTestResult('Testing signature saving...');

    try {
      if (customerSignature) {
        const result = await SignatureService.saveSignature(customerSignature, 1, 'test');
        addTestResult(`Customer signature saved: ${result.file_url}`);
      }

      if (mechanicSignature) {
        const result = await SignatureService.saveSignature(mechanicSignature, 1, 'test');
        addTestResult(`Mechanic signature saved: ${result.file_url}`);
      }
    } catch (error: any) {
      addTestResult(`Save error: ${error.message}`);
    }

    setIsLoading(false);
  };

  const testTemplateProcessing = () => {
    addTestResult('Testing template processing...');

    const testData = {
      klant_naam: 'Test Customer',
      klant_handtekening: customerSignature,
      monteur_naam: 'Test Mechanic',
      monteur_handtekening: mechanicSignature,
      datum: new Date().toISOString().split('T')[0]
    };

    try {
      const processedData = processSignaturesForTemplate(testData);
      addTestResult('Template processing successful');
      
      // Check if signatures were converted to image objects
      if (processedData.klant_handtekening && typeof processedData.klant_handtekening === 'object') {
        addTestResult('Customer signature converted to image object');
      }
      
      if (processedData.monteur_handtekening && typeof processedData.monteur_handtekening === 'object') {
        addTestResult('Mechanic signature converted to image object');
      }
      
      console.log('Processed data:', processedData);
    } catch (error: any) {
      addTestResult(`Template processing error: ${error.message}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const clearSignatures = () => {
    setCustomerSignature('');
    setMechanicSignature('');
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold text-amspm-text mb-6">Signature Functionality Test</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Signature Input Section */}
        <div className="space-y-6">
          <div className="card bg-white shadow-lg">
            <div className="card-content p-6">
              <h2 className="text-xl font-semibold mb-4">Test Signatures</h2>
              
              <div className="space-y-6">
                <SignatureField
                  name="customer_signature"
                  label="Customer Signature"
                  value={customerSignature}
                  onChange={handleFieldChange}
                />
                
                <SignatureField
                  name="mechanic_signature"
                  label="Mechanic Signature"
                  value={mechanicSignature}
                  onChange={handleFieldChange}
                />
              </div>
              
              <div className="flex gap-2 mt-6">
                <button
                  onClick={testSignatureValidation}
                  disabled={isLoading}
                  className="btn bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Test Validation
                </button>
                
                <button
                  onClick={testSignatureSaving}
                  disabled={isLoading}
                  className="btn bg-green-600 hover:bg-green-700 text-white"
                >
                  Test Saving
                </button>
                
                <button
                  onClick={testTemplateProcessing}
                  disabled={isLoading}
                  className="btn bg-purple-600 hover:bg-purple-700 text-white"
                >
                  Test Processing
                </button>
                
                <button
                  onClick={clearSignatures}
                  className="btn bg-gray-600 hover:bg-gray-700 text-white"
                >
                  Clear Signatures
                </button>
              </div>
            </div>
          </div>
        </div>
        
        {/* Test Results Section */}
        <div className="space-y-6">
          <div className="card bg-white shadow-lg">
            <div className="card-content p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Test Results</h2>
                <button
                  onClick={clearResults}
                  className="btn btn-sm bg-red-600 hover:bg-red-700 text-white"
                >
                  Clear Results
                </button>
              </div>
              
              <div className="bg-gray-100 p-4 rounded-lg max-h-96 overflow-y-auto">
                {testResults.length === 0 ? (
                  <p className="text-gray-500 italic">No test results yet...</p>
                ) : (
                  <div className="space-y-1">
                    {testResults.map((result, index) => (
                      <div key={index} className="text-sm font-mono">
                        {result}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Signature Preview */}
          <div className="card bg-white shadow-lg">
            <div className="card-content p-6">
              <h2 className="text-xl font-semibold mb-4">Signature Preview</h2>
              
              <div className="space-y-4">
                {customerSignature && (
                  <div>
                    <h3 className="font-medium mb-2">Customer Signature:</h3>
                    <img 
                      src={customerSignature} 
                      alt="Customer signature" 
                      className="border border-gray-300 rounded max-w-full h-auto"
                      style={{ maxHeight: '100px' }}
                    />
                  </div>
                )}
                
                {mechanicSignature && (
                  <div>
                    <h3 className="font-medium mb-2">Mechanic Signature:</h3>
                    <img 
                      src={mechanicSignature} 
                      alt="Mechanic signature" 
                      className="border border-gray-300 rounded max-w-full h-auto"
                      style={{ maxHeight: '100px' }}
                    />
                  </div>
                )}
                
                {!customerSignature && !mechanicSignature && (
                  <p className="text-gray-500 italic">No signatures to preview</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignatureTest;
